import { motion } from "framer-motion";
import { useState } from "react";
import { TbArrowLeft } from "react-icons/tb";

const SalesEmailTemplate = ({ variants, generatedInvoices, onNext, onBack }) => {
  const [emailSubject, setEmailSubject] = useState("Your Invoice from {{company}}");
  const [emailBody, setEmailBody] = useState(
`Dear {{customer_name}},

We have created an invoice #{{invoice_number}} for your recent purchase.

The total amount due is {{total_amount}}.

Please find the attached invoice for your records.

Thank you for your business!

Best regards,
{{signature}}
{{company}}`
  );
  const [senderEmail, setSenderEmail] = useState("");
  const [error, setError] = useState("");
  
  const handleNext = () => {
    if (!emailSubject.trim() || !emailBody.trim()) {
      setError("Email subject and body cannot be empty");
      return;
    }
    
    if (!senderEmail.trim()) {
      setError("Sender email is required");
      return;
    }
    
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(senderEmail)) {
      setError("Please enter a valid email address");
      return;
    }
    
    onNext({ emailSubject, emailBody, senderEmail });
  };
  
  // Helper to show a preview with variables replaced
  const getPreviewText = (text) => {
    return text
      .replace(/\{\{customer_name\}\}/g, "Sample Customer")
      .replace(/\{\{customer\}\}/g, "Sample Customer")
      .replace(/\{\{invoice_number\}\}/g, "INV-2023-001")
      .replace(/\{\{total_amount\}\}/g, "$1,320.00")
      .replace(/\{\{company\}\}/g, "Your Company Name")
      .replace(/\{\{signature\}\}/g, "John Doe");
  };

  return (
    <motion.div
      className="flex flex-col gap-6"
      variants={variants}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <motion.button
            onClick={onBack}
            className="w-12 h-12 rounded-full flex items-center justify-center bg-blue-600 text-white shadow-md hover:bg-blue-700 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            aria-label="Go back"
          >
            <TbArrowLeft className="h-6 w-6" />
          </motion.button>
          <div>
            <h2 className="text-xl font-medium text-gray-700">
              Customize Email Template
            </h2>
            <p className="text-gray-600 mt-1">
              Customize the email message that will be sent to your customers with their invoices
            </p>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-800 text-sm">{error}</p>
        </div>
      )}
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-700">Email Configuration</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                From Email Address
              </label>
              <input
                type="email"
                value={senderEmail}
                onChange={(e) => setSenderEmail(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-700"
                placeholder="<EMAIL>"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Subject
              </label>
              <input
                type="text"
                value={emailSubject}
                onChange={(e) => setEmailSubject(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-700"
                placeholder="Enter email subject"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Body
              </label>
              <textarea
                value={emailBody}
                onChange={(e) => setEmailBody(e.target.value)}
                rows={12}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-700"
                placeholder="Enter email message"
              />
            </div>
          </div>
          
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <h3 className="text-sm font-medium text-blue-800 mb-2">Available Variables:</h3>
            <ul className="space-y-1 text-sm text-blue-700">
              <li><code>{'{{customer_name}}'}</code> - Customer&apos;s name</li>
              <li><code>{'{{customer}}'}</code> - Customer&apos;s name (alternative)</li>
              <li><code>{'{{invoice_number}}'}</code> - Invoice number</li>
              <li><code>{'{{total_amount}}'}</code> - Total amount</li>
              <li><code>{'{{company}}'}</code> - Your company name</li>
              <li><code>{'{{signature}}'}</code> - Your signature</li>
            </ul>
          </div>
        </div>
        
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-700">Email Preview</h3>
          
          <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
            <div className="bg-gray-50 p-4 border-b">
              <p className="font-medium text-gray-800">From: {senderEmail || "<EMAIL>"}</p>
              <p className="font-medium text-gray-800 mt-2">Subject: {getPreviewText(emailSubject)}</p>
            </div>
            <div className="p-6 whitespace-pre-wrap text-gray-900">{getPreviewText(emailBody)}</div>
          </div>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <p className="text-sm text-yellow-800">
              <strong>Note:</strong> This is just a preview. The actual email will include 
              the specific customer information when sent.
            </p>
          </div>
        </div>
      </div>

      {/* Summary */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-700 mb-4">Email Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-600">Total Invoices:</span>
            <p className="text-gray-900">{generatedInvoices?.length || 0}</p>
          </div>
          <div>
            <span className="font-medium text-gray-600">From Email:</span>
            <p className="text-gray-900">{senderEmail || "Not set"}</p>
          </div>
          <div>
            <span className="font-medium text-gray-600">Subject:</span>
            <p className="text-gray-900">{emailSubject || "Not set"}</p>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-4">
        <button
          onClick={onBack}
          className="px-6 py-2 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors"
        >
          Back
        </button>
        <button
          onClick={handleNext}
          className="px-6 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
        >
          Continue to Send Emails
        </button>
      </div>
    </motion.div>
  );
};

export default SalesEmailTemplate;
